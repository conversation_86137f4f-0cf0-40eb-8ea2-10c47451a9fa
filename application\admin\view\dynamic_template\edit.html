{layout name="layout1" /}
<div class="layui-card">
    <div class="layui-card-header">
        <span>编辑动态模板</span>
        <div class="layui-btn-group layuiadmin-btn-group">
            <a class="layui-btn layui-btn-sm layui-btn-primary" href="{:url('lists')}">
                <i class="layui-icon layui-icon-return"></i>返回列表
            </a>
        </div>
    </div>
    
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="template-form">
            <div class="layui-row layui-col-space15">
                <!-- 基本信息 -->
                <div class="layui-col-md8">
                    <div class="layui-card">
                        <div class="layui-card-header">基本信息</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板ID <span class="layui-text-danger">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="template_id" value="{$info.template_id}" required lay-verify="required" 
                                           placeholder="请输入迅排模板ID" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">从迅排系统获取的模板ID</div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板标题 <span class="layui-text-danger">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" value="{$info.title}" required lay-verify="required" 
                                           placeholder="请输入模板标题" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">模板描述</label>
                                <div class="layui-input-block">
                                    <textarea name="description" placeholder="请输入模板描述" 
                                              class="layui-textarea" rows="3">{$info.description}</textarea>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">分类 <span class="layui-text-danger">*</span></label>
                                    <div class="layui-input-inline">
                                        <select name="category" required lay-verify="required">
                                            <option value="">请选择分类</option>
                                            {volist name="category_options" id="vo" key="k"}
                                            <option value="{$k}" {if condition="$k == $info.category"}selected{/if}>{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">状态</label>
                                    <div class="layui-input-inline">
                                        <select name="status">
                                            <option value="1" {if condition="$info.status == 1"}selected{/if}>启用</option>
                                            <option value="0" {if condition="$info.status == 0"}selected{/if}>禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">画布宽度 <span class="layui-text-danger">*</span></label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="width" value="{$info.width}" required lay-verify="required|number" 
                                               placeholder="像素" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">画布高度 <span class="layui-text-danger">*</span></label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="height" value="{$info.height}" required lay-verify="required|number" 
                                               placeholder="像素" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">标签</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tags" value="{if condition="is_array($info.tags)"}{$info.tags|implode=','}{else/}{$info.tags}{/if}" 
                                           placeholder="多个标签用逗号分隔" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">缩略图</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn layui-btn-normal" id="upload-thumbnail">
                                            <i class="layui-icon layui-icon-upload"></i>上传图片
                                        </button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="thumbnail-preview" 
                                                 src="{$info.thumbnail}" style="width: 100px; height: 60px; {if condition="empty($info.thumbnail)"}display: none;{/if}">
                                            <input type="hidden" name="thumbnail" id="thumbnail-input" value="{$info.thumbnail}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">排序</label>
                                <div class="layui-input-block">
                                    <input type="number" name="sort" value="{$info.sort|default=50}" class="layui-input" style="width: 100px;">
                                    <div class="layui-form-mid layui-word-aux">数值越大越靠前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 模板数据 -->
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            模板数据
                            <div class="layui-btn-group" style="float: right;">
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="load-template">
                                    <i class="layui-icon layui-icon-download-circle"></i>重新加载
                                </button>
                            </div>
                        </div>
                        <div class="layui-card-body">
                            <div class="layui-form-item layui-form-text">
                                <textarea name="template_data" placeholder="模板JSON数据" 
                                          class="layui-textarea" rows="15" required lay-verify="required">{if condition="is_array($info.template_data)"}{$info.template_data|json_encode=JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE}{else/}{$info.template_data}{/if}</textarea>
                                <div class="layui-form-mid layui-word-aux">从迅排API获取的模板数据</div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">文本元素数</label>
                                <div class="layui-input-block">
                                    <input type="number" name="text_elements_count" value="{$info.text_elements_count|default=0}" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">参数数量</label>
                                <div class="layui-input-block">
                                    <input type="number" name="param_count" value="{$info.param_count|default=0}" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 参数配置 -->
            <div class="layui-card" style="margin-top: 15px;">
                <div class="layui-card-header">
                    参数配置
                    <div class="layui-btn-group" style="float: right;">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="parse-params">
                            <i class="layui-icon layui-icon-refresh-3"></i>重新解析
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs" id="add-param">
                            <i class="layui-icon layui-icon-add-1"></i>添加参数
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div id="params-container">
                        {if condition="!empty($info.params)"}
                            {volist name="info.params" id="param" key="index"}
                            <div class="layui-card param-item" data-index="{$index}">
                                <div class="layui-card-header">
                                    参数 {$index + 1}
                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-param" style="float: right;">
                                        <i class="layui-icon layui-icon-delete"></i>删除
                                    </button>
                                </div>
                                <div class="layui-card-body">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">参数名称</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="params[{$index}][param_name]" 
                                                           value="{$param.param_name}" placeholder="参数名称" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">参数标签</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="params[{$index}][param_label]" 
                                                           value="{$param.param_label}" placeholder="参数标签" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">参数类型</label>
                                                <div class="layui-input-block">
                                                    <select name="params[{$index}][param_type]">
                                                        {volist name="param_type_options" id="vo" key="k"}
                                                        <option value="{$k}" {if condition="$k == $param.param_type"}selected{/if}>{$vo}</option>
                                                        {/volist}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">最大长度</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="params[{$index}][max_length]" 
                                                           value="{$param.max_length|default=100}" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">默认值</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="params[{$index}][default_value]" 
                                                   value="{$param.default_value}" placeholder="默认值" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">参数描述</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="params[{$index}][param_description]" 
                                                   value="{$param.param_description}" placeholder="参数描述" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="params[{$index}][is_required]" 
                                                   value="1" title="必填参数" {if condition="$param.is_required"}checked{/if}>
                                            <input type="hidden" name="params[{$index}][element_uuid]" value="{$param.element_uuid}">
                                            <input type="hidden" name="params[{$index}][original_text]" value="{$param.original_text}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        {else/}
                            <div class="layui-text-center layui-text-muted" style="padding: 20px;">
                                暂无参数配置，点击"重新解析"自动解析或手动添加
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
            
            <!-- 提交按钮 -->
            <div class="layui-form-item" style="margin-top: 30px;">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submit">
                        <i class="layui-icon layui-icon-ok"></i>保存修改
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <a class="layui-btn layui-btn-primary" href="{:url('lists')}">取消</a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 参数配置模板 -->
<script type="text/html" id="param-item-tpl">
    <div class="layui-card param-item" data-index="{{index}}">
        <div class="layui-card-header">
            参数 {{index + 1}}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-param" style="float: right;">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">参数名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="params[{{index}}][param_name]" 
                                   value="{{param_name}}" placeholder="参数名称" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">参数标签</label>
                        <div class="layui-input-block">
                            <input type="text" name="params[{{index}}][param_label]" 
                                   value="{{param_label}}" placeholder="参数标签" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">参数类型</label>
                        <div class="layui-input-block">
                            <select name="params[{{index}}][param_type]">
                                {volist name="param_type_options" id="vo" key="k"}
                                <option value="{$k}" {{param_type == '{$k}' ? 'selected' : ''}}>{$vo}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">最大长度</label>
                        <div class="layui-input-block">
                            <input type="number" name="params[{{index}}][max_length]" 
                                   value="{{max_length || 100}}" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">默认值</label>
                <div class="layui-input-block">
                    <input type="text" name="params[{{index}}][default_value]" 
                           value="{{default_value}}" placeholder="默认值" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">参数描述</label>
                <div class="layui-input-block">
                    <input type="text" name="params[{{index}}][param_description]" 
                           value="{{param_description}}" placeholder="参数描述" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="params[{{index}}][is_required]" 
                           value="1" title="必填参数" {{is_required ? 'checked' : ''}}>
                    <input type="hidden" name="params[{{index}}][element_uuid]" value="{{element_uuid}}">
                    <input type="hidden" name="params[{{index}}][original_text]" value="{{original_text}}">
                </div>
            </div>
        </div>
    </div>
</script>

<script>
layui.use(['form', 'upload', 'layer', 'laytpl', 'jquery'], function(){
    var form = layui.form,
        upload = layui.upload,
        layer = layui.layer,
        laytpl = layui.laytpl,
        $ = layui.jquery;
    
    var paramIndex = {$info.param_count|default=0};
    
    // 上传缩略图
    upload.render({
        elem: '#upload-thumbnail',
        url: '{:url("upload/image")}',
        accept: 'images',
        done: function(res){
            if(res.code == 1){
                $('#thumbnail-preview').attr('src', res.data.url).show();
                $('#thumbnail-input').val(res.data.path);
            } else {
                layer.msg(res.msg);
            }
        }
    });
    
    // 重新加载模板数据
    $('#load-template').click(function(){
        var templateId = $('input[name="template_id"]').val();
        if(!templateId){
            layer.msg('请先输入模板ID');
            return;
        }
        
        layer.confirm('重新加载将覆盖当前模板数据，确定继续吗？', function(index){
            layer.load(2);
            $.get('/api/template', {id: templateId}, function(res){
                layer.closeAll('loading');
                if(res.code == 200){
                    var data = res.data;
                    $('input[name="title"]').val(data.title);
                    $('textarea[name="description"]').val(data.description);
                    $('select[name="category"]').val(data.category);
                    $('input[name="width"]').val(data.width);
                    $('input[name="height"]').val(data.height);
                    $('input[name="text_elements_count"]').val(data.textElementsCount || 0);
                    $('textarea[name="template_data"]').val(JSON.stringify(data.data, null, 2));
                    
                    if(data.thumbnail){
                        $('#thumbnail-preview').attr('src', data.thumbnail).show();
                        $('#thumbnail-input').val(data.thumbnail);
                    }
                    
                    if(data.tags && data.tags.length > 0){
                        $('input[name="tags"]').val(data.tags.join(','));
                    }
                    
                    form.render();
                    layer.msg('模板数据重新加载成功');
                } else {
                    layer.msg('加载失败：' + res.message);
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('网络请求失败');
            });
            layer.close(index);
        });
    });
    
    // 重新解析参数
    $('#parse-params').click(function(){
        var templateId = $('input[name="template_id"]').val();
        if(!templateId){
            layer.msg('请先输入模板ID');
            return;
        }
        
        layer.confirm('重新解析将覆盖当前参数配置，确定继续吗？', function(index){
            layer.load(2);
            $.post('/api/template/parse', {templateId: templateId}, function(res){
                layer.closeAll('loading');
                if(res.code == 200 && res.data.parameterCandidates){
                    var candidates = res.data.parameterCandidates;
                    $('#params-container').empty();
                    paramIndex = 0;
                    
                    candidates.forEach(function(candidate){
                        addParamItem(candidate);
                    });
                    
                    $('input[name="param_count"]').val(candidates.length);
                    form.render();
                    layer.msg('参数重新解析成功，共解析到 ' + candidates.length + ' 个参数');
                } else {
                    layer.msg('解析失败：' + (res.message || '未知错误'));
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('网络请求失败');
            });
            layer.close(index);
        });
    });
    
    // 添加参数
    $('#add-param').click(function(){
        addParamItem({});
    });
    
    // 添加参数项
    function addParamItem(data){
        var getTpl = laytpl($('#param-item-tpl').html());
        data.index = paramIndex++;
        var html = getTpl.render(data);
        
        if($('#params-container .param-item').length == 0){
            $('#params-container').html(html);
        } else {
            $('#params-container').append(html);
        }
        
        form.render();
    }
    
    // 删除参数
    $(document).on('click', '.remove-param', function(){
        $(this).closest('.param-item').remove();
        updateParamCount();
    });
    
    // 更新参数数量
    function updateParamCount(){
        var count = $('#params-container .param-item').length;
        $('input[name="param_count"]').val(count);
    }
    
    // 表单提交
    form.on('submit(submit)', function(data){
        var field = data.field;
        
        // 验证模板数据是否为有效JSON
        try {
            JSON.parse(field.template_data);
        } catch(e) {
            layer.msg('模板数据格式错误，请检查JSON格式');
            return false;
        }
        
        layer.load(2);
        $.post('{:url("edit")}', field, function(res){
            layer.closeAll('loading');
            if(res.code == 1){
                layer.msg('保存成功', {icon: 1}, function(){
                    location.href = res.url;
                });
            } else {
                layer.msg(res.msg);
            }
        });
        
        return false;
    });
});
</script>
